# pinecone_utils.py
import logging
import struct
import json
import time
from pinecone import Pinecone, ServerlessSpec
from langchain_openai import OpenAIEmbeddings
from utils.pkg_res_version_checker import get_version
from config import (PINECONE_API_KEY_USER, PINECONE_ENVIRONMENT, OPENAI_API_KEY_ADMIN, OPENAI_API_KEY_USER,
                    OPENAI_API_KEY_DRIVE, PINECONE_API_KEY_ADMIN, EXPIRATION_TIME, PINECONE_API_KEY_DRIVE)
from langchain_pinecone import PineconeVectorStore
from utils.redis_util import get_redis_client


# Print the version of pinecone package
print("pinecone: ", get_version("pinecone"))

logger = logging.getLogger(__name__)


# Connect to Redis
r = get_redis_client()


def check_and_create_index(index_name, user_type):
    """
    Function to check if an index is present in Pinecone. If not present, it creates a new index.

    Args:
        index_name (str): The name of the index.
        user_type
    """
    # Initialize Pinecone
    pc = Pinecone(api_key=PINECONE_API_KEY_USER)

    if index_name not in pc.list_indexes().names():
        pc.create_index(
            name=index_name,
            dimension=1536,
            metric="cosine",
            spec=ServerlessSpec(
                cloud='aws',
                region=PINECONE_ENVIRONMENT
            )
        )
        print(f"Index {index_name} created.")
    else:
        print(f"{index_name} already exists")


def get_embeddings_from_langchain(is_admin=False):
    """
    Function to get embeddings from Langchain's OpenAI.

    Returns:
        OpenAIEmbeddings: The OpenAIEmbeddings object.
    """

    if is_admin:
        logger.info(f"Admin embeddings")
        embeddings = OpenAIEmbeddings(
            api_key=OPENAI_API_KEY_ADMIN
        )
        return embeddings
    else:
        logger.info(f"User embeddings")
        embeddings = OpenAIEmbeddings(
            api_key=OPENAI_API_KEY_USER
        )
        return embeddings


def get_embeddings_drive_from_langchain():
    """
    Function to get embeddings from Langchain's OpenAI.

    Returns:
        OpenAIEmbeddings: The OpenAIEmbeddings object.
    """

    logger.info(f"Drive embeddings")
    embeddings = OpenAIEmbeddings(
        api_key=OPENAI_API_KEY_DRIVE
    )
    return embeddings


def get_namespaces(namespace, index_name_val, user_type):
    """
    Function to get the namespaces from Pinecone.

    Args:
        namespace:
        index_name_val:
        user_type:

    Returns:
        boolean: value.
    """
    # Initialize Pinecone
    pc = Pinecone(api_key=PINECONE_API_KEY_USER)

    index = pc.Index(index_name_val)
    namespaces = index.describe_index_stats()['namespaces']
    return namespace in namespaces


def store_chunks_in_pinecone(data_chunks, namespace, index_name, user_type):
    """
    Function to store chunks in Pinecone using Langchain OpenAI Embedding.

    Args:
        data_chunks (list): The list of data chunks.
        namespace (str): The namespace.
        index_name (str): The name of the index.
        user_type (str): The name of the index.
    """
    # Initialize Pinecone
    logger.info(f"Storing chunks in Pinecone: {namespace}")
    pc = None
    embeddings = None
    if user_type.lower() == "user":
        pc = Pinecone(api_key=PINECONE_API_KEY_USER)
        embeddings = get_embeddings_from_langchain(True)
    elif user_type.lower() == "admin":
        pc = Pinecone(api_key=PINECONE_API_KEY_ADMIN)
        embeddings = get_embeddings_from_langchain(True)
    elif user_type.lower() == "drive":
        pc = Pinecone(api_key=PINECONE_API_KEY_DRIVE)
        embeddings = get_embeddings_drive_from_langchain()
    index = pc.Index(index_name)

    for chunk in data_chunks:
        # Get embeddings for the chunk from Langchain's OpenAI
        embeddings_response = embeddings.embed_query(chunk)
        # Store the embeddings in Pinecone
        index.upsert(
            [{"id": f"chunk-{str(data_chunks.index(chunk))}", "values": embeddings_response,
              "metadata": {"text": chunk}}],
            namespace=namespace)
    print(f"Namespace {namespace} created.")
    print(f"Chunks stored in {namespace}")


def store_embedding_in_redis(namespace, doc_id, embedding, metadata, user_type):
    # Convert embedding list to binary float32
    embedding_binary = struct.pack('f' * len(embedding), *embedding)

    metadata_json = json.dumps(metadata)

    # Construct the full key with namespace
    full_key = f"{namespace}:{doc_id}"

    # Store the embedding and metadata in Redis
    r.hset(full_key, mapping={
        'embedding': embedding_binary,
        'metadata': metadata_json
    })

    if user_type == "admin":
        # Set the expiration time if provided
        if EXPIRATION_TIME:
            r.expire(full_key, EXPIRATION_TIME)
    elif user_type == "drive":
        r.expire(full_key, 900)
    print(f"Stored {full_key} in Redis")


def process_chunks_admin(data_chunks, namespace):
    # Get embeddings (assuming this function returns an object with embed_query method)
    embeddings = get_embeddings_from_langchain(True)

    print(f"Storing data in Redis under namespace {namespace}")
    print(f"Total items: {len(data_chunks)}")

    # Check if we have a single full text document or multiple chunks
    if len(data_chunks) == 1:
        # Single full text document - store as full text
        full_text = data_chunks[0]
        print(f"Storing full text document (length: {len(full_text)} characters)")

        # Get embeddings for the full text
        embeddings_response = embeddings.embed_query(full_text)

        # Store as a single document with ID "fulltext"
        doc_id = "fulltext"
        print(f"Storing full text as {doc_id} in Redis")

        # Store the embeddings in Redis with the namespace
        store_embedding_in_redis(namespace, doc_id, embeddings_response, {"text": full_text}, "admin")
    else:
        # Multiple chunks - store as individual chunks (legacy behavior)
        for chunk in data_chunks:
            # Get embeddings for the chunk from Langchain's OpenAI
            embeddings_response = embeddings.embed_query(chunk)

            # Create a unique document ID based on index
            doc_id = f"chunk-{str(data_chunks.index(chunk))}"

            print(f"Storing chunk {doc_id} in Redis")

            # Store the embeddings in Redis with the namespace
            store_embedding_in_redis(namespace, doc_id, embeddings_response, {"text": chunk}, "admin")

    print(f"All data stored in Redis under namespace {namespace}")


def manage_index_and_namespace(index_name, data_chunks, namespace, user_type):
    """
    Function to manage index and namespace. It checks if an index exists and creates it if it does not exist.
    Then it stores chunks in Pinecone.

    Args:
        index_name (str): The name of the index.
        data_chunks (list): The list of data chunks.
        namespace (str): The namespace.
        user_type
    """
    # Check if index exists
    # Create the index if it does not exist
    if user_type.lower() == "user":
        logger.info(f"Checking and creating index: {index_name}")
        check_and_create_index(index_name, user_type)
    else:
        logger.info(f"User Type is {user_type}")

    # Store chunks in Pinecone
    logger.info(f"Storing chunks: {namespace}")
    if user_type.lower() == "admin":
        process_chunks_admin(data_chunks, namespace)
    elif user_type.lower() == "user":
        store_chunks_in_pinecone(data_chunks, namespace, index_name, user_type)
    elif user_type.lower() == "drive":
        process_chunks_ext(data_chunks, namespace)


def chunks(iterable, n):
    """
    Function to yield successive n-sized chunks from an iterable.

    Args:
        iterable (iterable): The iterable to be chunked.
        n (int): The size of each chunk.

    Yields:
        list: The next n-sized chunk from the iterable.
    """
    chunk = []
    for item in iterable:
        chunk.append(item)
        if len(chunk) == n:
            yield chunk
            chunk = []
    if chunk:
        yield chunk


def retrieve_data(query, namespace, index_name, user_type, retrieve_all=False):
    """
    Function to retrieve data from Pinecone. It can retrieve all vectors or the most similar vectors.

    Args:
        query (str): The query string.
        namespace (str): The namespace.
        index_name (str): The name of the index.
        retrieve_all (bool, optional): Whether to retrieve all vectors. Defaults to False.
        user_type (bool, optional): Whether to retrieve all vectors. Defaults to False.

    Returns:
        str or list: The retrieved texts.
    """
    # Initialize Pinecone
    pc = None
    if user_type.lower() == "user":
        pc = Pinecone(api_key=PINECONE_API_KEY_USER)
    elif user_type.lower() == "admin":
        pc = Pinecone(api_key=PINECONE_API_KEY_ADMIN)
    index = pc.Index(index_name)

    # Initialize Langchain's OpenAI
    if retrieve_all:
        embeddings = get_embeddings_from_langchain(True)
    else:
        embeddings = get_embeddings_from_langchain()

    # Embed the query
    query_vector = embeddings.embed_query(query)

    if retrieve_all:
        # Query Pinecone for all vectors
        namespaces = index.describe_index_stats()['namespaces']
        vector_count = namespaces[namespace]['vector_count']
        result = index.query(vector=query_vector, top_k=vector_count, namespace=namespace, include_metadata=True,
                             include_values=True)
        chunks_array = (match.metadata['text'] for match in result.matches)
        retrieved_texts = list(chunks(chunks_array, 10))
    else:
        # Query Pinecone for the most similar vectors
        result = index.query(vector=query_vector, top_k=10, namespace=namespace, include_metadata=True,
                             include_values=True)
        retrieved_texts = '\n'.join([match.metadata['text'] for match in result.matches])

    return retrieved_texts


def retrieve_embeddings_by_namespace(namespace):
    """
    Retrieve embeddings from Redis for a given namespace

    Args:
        namespace (str): The namespace to retrieve embeddings for

    Returns:
        list: List of Match objects containing metadata and embeddings
    """
    logger.info(f"[DB-DEBUG] Starting retrieve_embeddings_by_namespace for namespace: {namespace}")

    try:
        # Get all keys under the namespace
        pattern = f"{namespace}:*"
        logger.info(f"[DB-DEBUG] Searching Redis with pattern: {pattern}")

        start_time = time.time()
        keys = r.keys(pattern)
        elapsed_time = time.time() - start_time

        logger.info(f"[DB-DEBUG] Found {len(keys)} keys in {elapsed_time:.2f} seconds")

        if not keys:
            logger.warning(f"[DB-DEBUG] No keys found for namespace: {namespace}")
            return []

        retrieved_embeddings = []
        processed_keys = 0
        error_keys = 0

        for key in keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            try:
                # Retrieve the embedding and metadata
                logger.debug(f"[DB-DEBUG] Retrieving data for key: {key_str}")

                start_time = time.time()
                result = r.hgetall(key)
                elapsed_time = time.time() - start_time

                if not result:
                    logger.warning(f"[DB-DEBUG] No data found for key: {key_str}")
                    error_keys += 1
                    continue

                logger.debug(f"[DB-DEBUG] Retrieved data for key {key_str} in {elapsed_time:.4f} seconds")

                # Check if required fields exist
                if b'embedding' not in result or b'metadata' not in result:
                    logger.warning(f"[DB-DEBUG] Missing required fields in key {key_str}. Fields: {list(result.keys())}")
                    error_keys += 1
                    continue

                # Unpack the binary embedding
                embedding_binary = result[b'embedding']
                embedding = struct.unpack('f' * (len(embedding_binary) // 4), embedding_binary)

                # Parse the metadata JSON
                metadata_json = result[b'metadata'].decode('utf-8')
                metadata = json.loads(metadata_json)

                retrieved_embeddings.append({
                    'key': key_str,
                    'embedding': embedding,
                    'metadata': metadata
                })

                processed_keys += 1

            except Exception as e:
                logger.error(f"[DB-DEBUG] Error processing key {key_str}: {str(e)}")
                error_keys += 1

        logger.info(f"[DB-DEBUG] Successfully processed {processed_keys} keys, errors: {error_keys}")

        # Return results similar to Pinecone's match structure
        class Match:
            def __init__(self, metadata, values):
                self.metadata = metadata
                self.values = values

        matches = [Match(metadata=item['metadata'], values=item['embedding']) for item in retrieved_embeddings]
        logger.info(f"[DB-DEBUG] Returning {len(matches)} matches")

        return matches
    except Exception as e:
        logger.error(f"[DB-DEBUG] Error in retrieve_embeddings_by_namespace: {str(e)}", exc_info=True)
        raise


def retrieve_text_chunks_from_redis(namespace):
    """
    Retrieve text data from Redis for a given namespace.
    Returns full text as a single item if stored as full text, otherwise returns chunks grouped into batches.

    Args:
        namespace (str): The namespace to retrieve data from

    Returns:
        list: List containing either full text as single item or text chunks grouped into batches of 10
    """
    logger.info(f"[DB-DEBUG] Starting retrieve_text_chunks_from_redis for namespace: {namespace}")

    try:
        logger.info(f"[DB-DEBUG] Calling retrieve_embeddings_by_namespace for namespace: {namespace}")
        start_time = time.time()
        result = retrieve_embeddings_by_namespace(namespace)
        elapsed_time = time.time() - start_time

        logger.info(f"[DB-DEBUG] Retrieved {len(result)} embeddings in {elapsed_time:.2f} seconds")

        if not result:
            logger.warning(f"[DB-DEBUG] No embeddings found for namespace: {namespace}")
            return []

        # Check if we have a single full text document
        if len(result) == 1:
            # Check if this is a full text document (stored with doc_id "fulltext")
            match = result[0]
            full_text = match.metadata['text']
            logger.info(f"[DB-DEBUG] Found single document with {len(full_text)} characters")

            # Return as a single item list containing the full text
            logger.info(f"[DB-DEBUG] Returning full text as single document")
            return [full_text]
        else:
            # Multiple chunks - group into batches (legacy behavior)
            logger.info(f"[DB-DEBUG] Extracting text from embeddings metadata")
            chunks_array = (match.metadata['text'] for match in result)

            logger.info(f"[DB-DEBUG] Grouping text chunks into batches of 10")
            retrieved_texts = list(chunks(chunks_array, 10))

            logger.info(f"[DB-DEBUG] Successfully retrieved {len(retrieved_texts)} batches of text chunks")
            return retrieved_texts
    except Exception as e:
        logger.error(f"[DB-DEBUG] Error in retrieve_text_chunks_from_redis: {str(e)}", exc_info=True)
        raise


def get_embeddings(sentences):
    oaiembeds = OpenAIEmbeddings(
        api_key=OPENAI_API_KEY_ADMIN
    )
    embeddings = oaiembeds.embed_documents([x['combined_sentence'] for x in sentences])
    return embeddings


def get_vectorstore(namespace, index, user_type):
    vectorstore = None
    if user_type.lower() == "user":
        vectorstore = PineconeVectorStore(
            index_name=index,
            embedding=OpenAIEmbeddings(api_key=OPENAI_API_KEY_USER),
            namespace=namespace,
            pinecone_api_key=PINECONE_API_KEY_USER
        )
    elif user_type.lower() == "admin":
        vectorstore = PineconeVectorStore(
            index_name=index,
            embedding=OpenAIEmbeddings(api_key=OPENAI_API_KEY_USER),
            namespace=namespace,
            pinecone_api_key=PINECONE_API_KEY_ADMIN
        )
    elif user_type.lower() == "drive":
        vectorstore = PineconeVectorStore(
            index_name=index,
            embedding=OpenAIEmbeddings(api_key=OPENAI_API_KEY_DRIVE),
            namespace=namespace,
            pinecone_api_key=PINECONE_API_KEY_DRIVE
        )

    return vectorstore.as_retriever()


def delete_old_ns(count, index_name_val, usertype):
    pc = None
    if usertype == "user":
        pc = Pinecone(api_key=PINECONE_API_KEY_USER)
    elif usertype == "admin":
        pc = Pinecone(api_key=PINECONE_API_KEY_ADMIN)

    index = pc.Index(index_name_val)
    namespaces = index.describe_index_stats()['namespaces']
    keys_to_delete = list(namespaces.keys())[:count]
    for key in keys_to_delete:
        index.delete(namespace=key, delete_all=True)

    return "Deleted namespaces"


def process_chunks_ext(data_chunks, namespace):
    # Get embeddings (assuming this function returns an object with embed_query method)
    embeddings = get_embeddings_drive_from_langchain()
    logger.info(f"Storing chunks in Redis under namespace {namespace} for extension")
    logger.info(f"Total chunks: {len(data_chunks)}")

    for chunk in data_chunks:
        # Get embeddings for the chunk from Langchain's OpenAI
        embeddings_response = embeddings.embed_query(chunk)

        # Create a unique document ID based on index
        doc_id = f"chunk-{str(data_chunks.index(chunk))}"

        logger.info(f"Storing chunk {doc_id} in Redis")

        # Store the embeddings in Redis with the namespace
        store_embedding_in_redis(namespace, doc_id, embeddings_response, {"text": chunk}, "drive")

    logger.info(f"All chunks stored in Redis under namespace {namespace}")
